package i18n

import (
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFinancialsheetTranslationsExist verifies that all new financialsheet translations exist in all languages
func TestFinancialsheetTranslationsExist(t *testing.T) {
	// Initialize the i18n service
	service, err := New()
	require.NoError(t, err, "Failed to initialize i18n service")

	// Test translation keys that we added for financialsheet
	newTranslationKeys := []string{
		// Service layer errors
		errors.KeyFinancialSheetErrorInvalidMonth,
		errors.KeyFinancialSheetErrorDuplicateMonth,
		errors.KeyFinancialSheetErrorSameMonthAsOriginal,
		errors.KeyFinancialSheetErrorRecordAlreadyExists,
		errors.KeyFinancialSheetErrorInvalidRecord,
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked,
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate,
		errors.KeyFinancialSheetErrorCannotMarkSameDayAsTransaction,

		// Controller layer errors
		errors.KeyFinancialSheetErrorInvalidFinancialSheetId,
		errors.KeyFinancialSheetErrorInvalidMonthParameter,
		errors.KeyFinancialSheetErrorInvalidYearParameter,
		errors.KeyFinancialSheetErrorInvalidInput,
		errors.KeyFinancialSheetErrorValidationFailed,
		errors.KeyFinancialSheetErrorDreamTransactionInput,
		errors.KeyFinancialSheetErrorDreamTransactionValidation,
		errors.KeyFinancialSheetErrorInvalidTransactionId,
	}

	// Test all supported languages
	languages := []string{"pt", "en", "es"}

	for _, lang := range languages {
		t.Run("Language_"+lang, func(t *testing.T) {
			for _, key := range newTranslationKeys {
				t.Run("Key_"+key, func(t *testing.T) {
					// Act
					translation := service.Translate(lang, key)

					// Assert
					assert.NotEmpty(t, translation, "Translation should not be empty for key %s in language %s", key, lang)
					assert.NotEqual(t, key, translation, "Translation should not be the same as the key for %s in language %s", key, lang)

					// Verify the translation contains expected characteristics for gaming/educational app
					switch lang {
					case "pt":
						// Portuguese translations should be friendly and use exclamations
						assert.True(t,
							len(translation) > 10,
							"Portuguese translation should be descriptive for key %s", key)
					case "en":
						// English translations should be friendly
						assert.True(t,
							len(translation) > 10,
							"English translation should be descriptive for key %s", key)
					case "es":
						// Spanish translations should be friendly
						assert.True(t,
							len(translation) > 10,
							"Spanish translation should be descriptive for key %s", key)
					}
				})
			}
		})
	}
}

// TestFinancialsheetTranslationConsistency verifies that translations are consistent across languages
func TestFinancialsheetTranslationConsistency(t *testing.T) {
	// Initialize the i18n service
	service, err := New()
	require.NoError(t, err, "Failed to initialize i18n service")

	// Test a few key translations for consistency
	testCases := []struct {
		key                string
		expectedPtContains []string
		expectedEnContains []string
		expectedEsContains []string
	}{
		{
			key:                errors.KeyFinancialSheetErrorInvalidMonth,
			expectedPtContains: []string{"mês", "1", "12"},
			expectedEnContains: []string{"month", "1", "12"},
			expectedEsContains: []string{"mes", "1", "12"},
		},
		{
			key:                errors.KeyFinancialSheetErrorDuplicateMonth,
			expectedPtContains: []string{"já", "mês"},
			expectedEnContains: []string{"already", "month"},
			expectedEsContains: []string{"ya", "mes"},
		},
		{
			key:                errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked,
			expectedPtContains: []string{"já", "sem transações", "hoje"},
			expectedEnContains: []string{"already", "no transactions", "today"},
			expectedEsContains: []string{"Ya", "sin transacciones", "hoy"},
		},
	}

	for _, tc := range testCases {
		t.Run("Key_"+tc.key, func(t *testing.T) {
			// Get translations
			ptTranslation := service.Translate("pt", tc.key)
			enTranslation := service.Translate("en", tc.key)
			esTranslation := service.Translate("es", tc.key)

			// Verify Portuguese translation contains expected terms
			for _, term := range tc.expectedPtContains {
				assert.Contains(t, ptTranslation, term,
					"Portuguese translation should contain '%s' for key %s", term, tc.key)
			}

			// Verify English translation contains expected terms
			for _, term := range tc.expectedEnContains {
				assert.Contains(t, enTranslation, term,
					"English translation should contain '%s' for key %s", term, tc.key)
			}

			// Verify Spanish translation contains expected terms
			for _, term := range tc.expectedEsContains {
				assert.Contains(t, esTranslation, term,
					"Spanish translation should contain '%s' for key %s", term, tc.key)
			}
		})
	}
}

// TestFinancialsheetTranslationFallback verifies that fallback works for missing keys
func TestFinancialsheetTranslationFallback(t *testing.T) {
	// Initialize the i18n service
	service, err := New()
	require.NoError(t, err, "Failed to initialize i18n service")

	// Test with a non-existent key
	nonExistentKey := "financialsheet.error.nonExistentKey"

	// Test all supported languages
	languages := []string{"pt", "en", "es"}

	for _, lang := range languages {
		t.Run("Language_"+lang, func(t *testing.T) {
			// Act
			translation := service.Translate(lang, nonExistentKey)

			// Assert - should fallback to the key itself
			assert.Equal(t, nonExistentKey, translation,
				"Should fallback to key when translation not found for language %s", lang)
		})
	}
}
