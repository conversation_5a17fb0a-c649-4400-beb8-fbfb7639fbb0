package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestServiceErrorsHaveTranslationKeys tests that all service layer errors use translation keys
func TestServiceErrorsHaveTranslationKeys(t *testing.T) {
	tests := []struct {
		name           string
		setupFunc      func(*service) error
		expectedKey    string
		expectedKind   errors.Kind
	}{
		{
			name: "validateRecurrenceMonths - invalid month",
			setupFunc: func(s *service) error {
				return s.validateRecurrenceMonths([]int{13}, time.Now())
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidMonth,
			expectedKind: errors.Validation,
		},
		{
			name: "validateRecurrenceMonths - duplicate month",
			setupFunc: func(s *service) error {
				return s.validateRecurrenceMonths([]int{1, 1}, time.Now())
			},
			expectedKey:  errors.KeyFinancialSheetErrorDuplicateMonth,
			expectedKind: errors.Validation,
		},
		{
			name: "validateRecurrenceMonths - same month as original",
			setupFunc: func(s *service) error {
				now := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
				return s.validateRecurrenceMonths([]int{1}, now) // January
			},
			expectedKey:  errors.KeyFinancialSheetErrorSameMonthAsOriginal,
			expectedKind: errors.Validation,
		},
		{
			name: "NoTransactions - invalid record",
			setupFunc: func(s *service) error {
				return s.NoTransactions(context.Background(), nil)
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidRecord,
			expectedKind: errors.Validation,
		},
		{
			name: "validateNoTransactionRequest - already marked today",
			setupFunc: func(s *service) error {
				today := time.Now()
				todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
				record := &financialsheet.Record{
					Points: financialsheet.Points{
						LastNoTransactionDate: todayNormalized,
					},
				}
				return s.validateNoTransactionRequest(record, todayNormalized)
			},
			expectedKey:  errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked,
			expectedKind: errors.Conflict,
		},
		{
			name: "validateNoTransactionRequest - already marked for date",
			setupFunc: func(s *service) error {
				today := time.Now()
				todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
				record := &financialsheet.Record{
					Points: financialsheet.Points{
						NoTransactionDates: []time.Time{todayNormalized},
					},
				}
				return s.validateNoTransactionRequest(record, todayNormalized)
			},
			expectedKey:  errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate,
			expectedKind: errors.Conflict,
		},
		{
			name: "validateNoTransactionRequest - same day as transaction",
			setupFunc: func(s *service) error {
				today := time.Now()
				todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
				record := &financialsheet.Record{
					Points: financialsheet.Points{
						LastTransactionDate: todayNormalized,
					},
				}
				return s.validateNoTransactionRequest(record, todayNormalized)
			},
			expectedKey:  errors.KeyFinancialSheetErrorCannotMarkSameDayAsTransaction,
			expectedKind: errors.Conflict,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			mockRepo := &MockRepository{}
			mockLeague := &MockLeagueService{}
			service := &service{
				Repository:    mockRepo,
				LeagueService: mockLeague,
			}

			// Act
			err := tt.setupFunc(service)

			// Assert
			assert.Error(t, err)
			
			domainErr, ok := err.(*errors.DomainError)
			assert.True(t, ok, "Error should be a DomainError")
			assert.Equal(t, tt.expectedKind, domainErr.Kind())
			assert.Equal(t, tt.expectedKey, domainErr.TranslationKey())
		})
	}
}

// TestInitializeErrorHasTranslationKey tests that Initialize method uses translation keys
func TestInitializeErrorHasTranslationKey(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}
	
	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Mock repository to return an existing record (conflict scenario)
	existingRecord := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             10,
			Best:                15,
			LastTransactionDate: time.Now(),
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(1000),
	}
	mockRepo.On("FindByUser", ctx, "user123").Return(existingRecord, nil)

	// Act
	err := service.Initialize(ctx, "user123", "Test User")

	// Assert
	assert.Error(t, err)
	
	domainErr, ok := err.(*errors.DomainError)
	assert.True(t, ok, "Error should be a DomainError")
	assert.Equal(t, errors.Conflict, domainErr.Kind())
	assert.Equal(t, errors.KeyFinancialSheetErrorRecordAlreadyExists, domainErr.TranslationKey())
}
