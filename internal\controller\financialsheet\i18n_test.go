package financialsheet

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockService for testing controller layer
type MockService struct {
	mock.Mock
}

func (m *MockService) Create(ctx echo.Context, record *financialsheet.Record) (string, error) {
	args := m.Called(ctx, record)
	return args.String(0), args.Error(1)
}

func (m *MockService) Find(ctx echo.Context, id string) (*financialsheet.Record, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUser(ctx echo.Context, userID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUsers(ctx echo.Context, userIDs []string) (map[string]*financialsheet.Record, error) {
	args := m.Called(ctx, userIDs)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUserAndPeriod(ctx echo.Context, userID string, year, month int, flatten, planning bool) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID, year, month, flatten, planning)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) Update(ctx echo.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockService) Delete(ctx echo.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockService) CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	args := m.Called(ctx, category, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindCategory(ctx echo.Context, id string) (*financialsheet.Category, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindAllCategories(ctx echo.Context) ([]*financialsheet.Category, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindCategoryByUserAndIdentifier(ctx echo.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	args := m.Called(ctx, userID, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) UpdateCategory(ctx echo.Context, category *financialsheet.Category) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

func (m *MockService) DeleteCategory(ctx echo.Context, id string, userID string) error {
	args := m.Called(ctx, id, userID)
	return args.Error(0)
}

func (m *MockService) CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, isRecurring bool) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transaction, isRecurring)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) CreateRecurringTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, recurrenceMonths []int) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transaction, recurrenceMonths)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) CreateDreamTransaction(ctx context.Context, userID string, dreamID string, amount monetary.Amount) error {
	args := m.Called(ctx, userID, dreamID, amount)
	return args.Error(0)
}

func (m *MockService) FindTransaction(ctx echo.Context, recordID, transactionID string) (*financialsheet.Transaction, error) {
	args := m.Called(ctx, recordID, transactionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Transaction), args.Error(1)
}

func (m *MockService) FindAllTransactions(ctx echo.Context, userID string, categoryType financialsheet.CategoryType, year, month int, planning bool) ([]financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month, planning)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]financialsheet.Transaction), args.Error(1)
}

func (m *MockService) UpdateTransaction(ctx echo.Context, record *financialsheet.Record, transactionID string, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transactionID, transaction)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) DeleteTransaction(ctx echo.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transactionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) NoTransactions(ctx echo.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockService) Initialize(ctx echo.Context, userID, userName string) error {
	args := m.Called(ctx, userID, userName)
	return args.Error(0)
}

func (m *MockService) CountUserCategories(ctx context.Context, userID string) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// TestControllerValidationErrorsHaveTranslationKeys tests that controller validation errors use translation keys
func TestControllerValidationErrorsHaveTranslationKeys(t *testing.T) {
	// Test that the translation keys are properly defined and follow the expected pattern
	translationKeys := []struct {
		name string
		key  string
	}{
		{"Invalid Financial Sheet ID", errors.KeyFinancialSheetErrorInvalidFinancialSheetId},
		{"Invalid Month Parameter", errors.KeyFinancialSheetErrorInvalidMonthParameter},
		{"Invalid Year Parameter", errors.KeyFinancialSheetErrorInvalidYearParameter},
		{"Invalid Input", errors.KeyFinancialSheetErrorInvalidInput},
		{"Validation Failed", errors.KeyFinancialSheetErrorValidationFailed},
		{"Dream Transaction Input", errors.KeyFinancialSheetErrorDreamTransactionInput},
		{"Dream Transaction Validation", errors.KeyFinancialSheetErrorDreamTransactionValidation},
		{"Invalid Transaction ID", errors.KeyFinancialSheetErrorInvalidTransactionId},
	}

	for _, tt := range translationKeys {
		t.Run(tt.name, func(t *testing.T) {
			// Verify the key follows the expected pattern
			assert.NotEmpty(t, tt.key, "Translation key should not be empty")
			assert.Contains(t, tt.key, "financialsheet.error.", "Translation key should contain 'financialsheet.error.'")
			assert.Regexp(t, `^financialsheet\.error\.[a-zA-Z][a-zA-Z0-9]*$`, tt.key,
				"Translation key should follow pattern: financialsheet.error.specificError")
		})
	}
}
