package financialsheet

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockService for testing controller layer
type MockService struct {
	mock.Mock
}

func (m *MockService) Create(ctx echo.Context, record *financialsheet.Record) (string, error) {
	args := m.Called(ctx, record)
	return args.String(0), args.Error(1)
}

func (m *MockService) Find(ctx echo.Context, id string) (*financialsheet.Record, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUser(ctx echo.Context, userID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUsers(ctx echo.Context, userIDs []string) (map[string]*financialsheet.Record, error) {
	args := m.Called(ctx, userIDs)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindByUserAndPeriod(ctx echo.Context, userID string, year, month int, flatten, planning bool) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID, year, month, flatten, planning)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) Update(ctx echo.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockService) Delete(ctx echo.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockService) CreateCategory(ctx echo.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	args := m.Called(ctx, category, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindCategory(ctx echo.Context, id string) (*financialsheet.Category, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindAllCategories(ctx echo.Context) ([]*financialsheet.Category, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Category), args.Error(1)
}

func (m *MockService) FindCategoryByUserAndIdentifier(ctx echo.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	args := m.Called(ctx, userID, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockService) UpdateCategory(ctx echo.Context, category *financialsheet.Category) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

func (m *MockService) DeleteCategory(ctx echo.Context, id string, userID string) error {
	args := m.Called(ctx, id, userID)
	return args.Error(0)
}

func (m *MockService) CreateTransaction(ctx echo.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, isRecurring bool) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transaction, isRecurring)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) CreateRecurringTransaction(ctx echo.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, recurrenceMonths []int) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transaction, recurrenceMonths)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) CreateDreamTransaction(ctx echo.Context, record *financialsheet.Record, dreamID, dreamName string, value int, date string, paymentMethod financialsheet.PaymentMethod) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, dreamID, dreamName, value, date, paymentMethod)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) FindTransaction(ctx echo.Context, recordID, transactionID string) (*financialsheet.Transaction, error) {
	args := m.Called(ctx, recordID, transactionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Transaction), args.Error(1)
}

func (m *MockService) FindAllTransactions(ctx echo.Context, userID string, categoryType financialsheet.CategoryType, year, month int, planning bool) ([]financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month, planning)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]financialsheet.Transaction), args.Error(1)
}

func (m *MockService) UpdateTransaction(ctx echo.Context, record *financialsheet.Record, transactionID string, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transactionID, transaction)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) DeleteTransaction(ctx echo.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, record, transactionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockService) NoTransactions(ctx echo.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockService) Initialize(ctx echo.Context, userID, userName string) error {
	args := m.Called(ctx, userID, userName)
	return args.Error(0)
}

func (m *MockService) CountUserCategories(ctx context.Context, userID string) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// TestControllerValidationErrorsHaveTranslationKeys tests that controller validation errors use translation keys
func TestControllerValidationErrorsHaveTranslationKeys(t *testing.T) {
	tests := []struct {
		name         string
		setupRequest func() (*http.Request, echo.Context)
		expectedKey  string
		expectedKind errors.Kind
	}{
		{
			name: "Find - invalid financial sheet ID",
			setupRequest: func() (*http.Request, echo.Context) {
				e := echo.New()
				req := httptest.NewRequest(http.MethodGet, "/financialsheets/", nil)
				rec := httptest.NewRecorder()
				c := e.NewContext(req, rec)
				c.SetParamNames("id")
				c.SetParamValues("") // Empty ID
				return req, c
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidFinancialSheetId,
			expectedKind: errors.Validation,
		},
		{
			name: "FindByUser - invalid month parameter",
			setupRequest: func() (*http.Request, echo.Context) {
				e := echo.New()
				req := httptest.NewRequest(http.MethodGet, "/financialsheets/user?month=13", nil)
				rec := httptest.NewRecorder()
				c := e.NewContext(req, rec)
				return req, c
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidMonthParameter,
			expectedKind: errors.Validation,
		},
		{
			name: "FindByUser - invalid year parameter",
			setupRequest: func() (*http.Request, echo.Context) {
				e := echo.New()
				req := httptest.NewRequest(http.MethodGet, "/financialsheets/user?year=invalid", nil)
				rec := httptest.NewRecorder()
				c := e.NewContext(req, rec)
				return req, c
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidYearParameter,
			expectedKind: errors.Validation,
		},
		{
			name: "CreateTransaction - invalid input",
			setupRequest: func() (*http.Request, echo.Context) {
				e := echo.New()
				invalidJSON := `{"invalid": json}`
				req := httptest.NewRequest(http.MethodPost, "/financialsheets/transactions", strings.NewReader(invalidJSON))
				req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
				rec := httptest.NewRecorder()
				c := e.NewContext(req, rec)
				return req, c
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidInput,
			expectedKind: errors.Validation,
		},
		{
			name: "FindTransaction - invalid transaction ID",
			setupRequest: func() (*http.Request, echo.Context) {
				e := echo.New()
				req := httptest.NewRequest(http.MethodGet, "/financialsheets/transactions/", nil)
				rec := httptest.NewRecorder()
				c := e.NewContext(req, rec)
				c.SetParamNames("id")
				c.SetParamValues("") // Empty transaction ID
				return req, c
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidTransactionId,
			expectedKind: errors.Validation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			mockService := &MockService{}
			controller := &controller{Service: mockService}

			req, c := tt.setupRequest()

			// Mock JWT token for authenticated endpoints
			c.Set("user", map[string]interface{}{
				"uid": "test-user-id",
			})

			var err error

			// Act - Call the appropriate handler based on test case
			switch {
			case strings.Contains(tt.name, "Find - invalid"):
				handler := controller.Find()
				err = handler(c)
			case strings.Contains(tt.name, "FindByUser"):
				handler := controller.FindByUser()
				err = handler(c)
			case strings.Contains(tt.name, "CreateTransaction"):
				handler := controller.CreateTransaction()
				err = handler(c)
			case strings.Contains(tt.name, "FindTransaction"):
				handler := controller.FindTransaction()
				err = handler(c)
			}

			// Assert
			assert.Error(t, err)

			domainErr, ok := err.(*errors.DomainError)
			assert.True(t, ok, "Error should be a DomainError, got: %T", err)
			assert.Equal(t, tt.expectedKind, domainErr.Kind())
			assert.Equal(t, tt.expectedKey, domainErr.TranslationKey())
		})
	}
}
